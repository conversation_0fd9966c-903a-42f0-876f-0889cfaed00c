import crypto from 'crypto';

/**
 * 从Cookie中提取h5Token
 * @param {string} cookie 
 * @returns {string}
 */
export function extractH5Token(cookie) {
    const match = cookie.match(/_m_h5_tk=([^;_]+)/);
    const token = match ? match[1] : '';
    // 清理token，移除可能的无效字符
    return token.replace(/[\r\n\t\s]/g, '');
}

/**
 * 从响应中解析Set-Cookie
 * @param {Response} response
 * @returns {object}
 */
export function parseSetCookieFromResponse(response) {
    const cookies = {};
    let hasNewCookies = false;

    try {
        // 获取所有set-cookie头
        const setCookieHeaders = response.headers.raw()['set-cookie'];

        if (setCookieHeaders && setCookieHeaders.length > 0) {
            setCookieHeaders.forEach(cookieHeader => {
                // 解析cookie字符串：name=value; path=/; domain=.taobao.com
                const cookieParts = cookieHeader.split(';')[0].split('=');
                if (cookieParts.length >= 2) {
                    const name = cookieParts[0].trim();
                    const value = cookieParts.slice(1).join('=').trim(); // 处理value中可能包含=的情况

                    if (name && value) {
                        cookies[name] = value;
                        hasNewCookies = true;
                    }
                }
            });
        }
    } catch (error) {
        console.error('解析Set-Cookie头失败:', error);
    }

    return {
        hasNewCookies,
        cookies
    };
}

/**
 * 更新主播Cookie
 * @param {string} anchorName 
 * @param {object} newCookies 
 * @param {string} currentCookie 
 * @returns {string}
 */
export function updateAnchorCookie(anchorName, newCookies, currentCookie) {
    if (!newCookies || Object.keys(newCookies).length === 0) {
        return currentCookie;
    }

    console.log(`[${anchorName}] 开始更新Cookie，新Cookie数量: ${Object.keys(newCookies).length}`);

    // 解析当前Cookie
    const currentCookieMap = {};
    if (currentCookie) {
        currentCookie.split(';').forEach(cookie => {
            const parts = cookie.trim().split('=');
            if (parts.length === 2) {
                currentCookieMap[parts[0]] = parts[1];
            }
        });
    }

    // 合并新Cookie
    Object.keys(newCookies).forEach(name => {
        const oldValue = currentCookieMap[name];
        const newValue = newCookies[name];
        
        if (oldValue !== newValue) {
            console.log(`[${anchorName}] 更新Cookie: ${name} = ${newValue.substring(0, 20)}...`);
            currentCookieMap[name] = newValue;
        }
    });

    // 重新组装Cookie字符串
    const updatedCookie = Object.keys(currentCookieMap)
        .map(name => `${name}=${currentCookieMap[name]}`)
        .join('; ');

    console.log(`[${anchorName}] Cookie更新完成`);
    return updatedCookie;
}



/**
 * 清理Cookie字符串
 * @param {string} cookie 
 * @returns {string}
 */
export function cleanCookie(cookie) {
    if (!cookie) return '';
    
    return cookie
        .replace(/[\r\n\t]/g, '')     // 移除换行符和制表符
        .replace(/[\x00-\x1F\x7F]/g, '') // 移除控制字符
        .replace(/[^\x20-\x7E]/g, '')     // 只保留可打印ASCII字符
        .replace(/\s+/g, ' ')         // 将多个空格替换为单个空格
        .trim();                      // 移除首尾空格
}
