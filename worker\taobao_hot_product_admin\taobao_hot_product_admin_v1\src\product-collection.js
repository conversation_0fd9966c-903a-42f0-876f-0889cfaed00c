import crypto from 'crypto';
import { database } from './database.js';
import fetch from 'node-fetch';

/**
 * 商品采集工具类
 */
class ProductCollection {
    constructor() {
        this.appKey = '12574478';
        this.api = 'mtop.taobao.cic.downlink.newstation.itemcenter.item.query';
        this.version = '1.0';
        this.baseUrl = 'https://h5api.m.taobao.com/h5';
    }

    /**
     * MD5哈希函数
     * @param {string} string
     * @returns {string}
     */
    md5(string) {
        return crypto.createHash('md5').update(string, 'utf8').digest('hex');
    }

    /**
     * 从Cookie中提取h5Token
     * @param {string} cookie
     * @returns {string}
     */
    extractH5Token(cookie) {
        const match = cookie.match(/_m_h5_tk=([^;_]+)/);
        const token = match ? match[1] : '';
        // 清理token，移除可能的无效字符
        return token.replace(/[\r\n\t\s]/g, '');
    }

    /**
     * 生成签名
     * @param {string} h5Token 
     * @param {number} timestamp 
     * @param {object} data 
     * @returns {string}
     */
    generateSign(h5Token, timestamp, data) {
        const dataStr = JSON.stringify(data);
        const signString = `${h5Token}&${timestamp}&${this.appKey}&${dataStr}`;
        return this.md5(signString);
    }

    /**
     * 构建请求URL
     * @param {string} h5Token 
     * @param {object} data 
     * @returns {object}
     */
    buildRequestUrl(h5Token, data) {
        const timestamp = Date.now();
        const sign = this.generateSign(h5Token, timestamp, data);
        
        const params = new URLSearchParams({
            jsv: '2.7.0',
            appKey: this.appKey,
            t: timestamp.toString(),
            sign: sign,
            api: this.api,
            v: this.version,
            valueType: 'original',
            preventFallback: 'true',
            type: 'originaljson',
            dataType: 'json',
            data: JSON.stringify(data)
        });

        return {
            url: `${this.baseUrl}/${this.api}/${this.version}/?${params.toString()}`,
            timestamp,
            sign
        };
    }

    /**
     * 获取商品数据
     * @param {string} anchorCookie
     * @param {object} filters
     * @param {number} pageNum
     * @param {number} pageSize
     * @returns {Promise<object>}
     */
    async fetchProducts(anchorCookie, filters, pageNum = 1, pageSize = 30) {
        try {
            const h5Token = this.extractH5Token(anchorCookie);
            if (!h5Token) {
                return {
                    success: false,
                    error: 'Cookie中未找到h5Token',
                    data: []
                };
            }

            // 构建请求数据
            const requestData = {
                pageNum,
                pageSize,
                filedKey: "itemRankListInfo",
                rankType: "1",
                topnItemList: null,
                recommendTabType: 0,
                code: filters.code || "",
                ...filters
            };

            // 移除code字段避免重复
            delete requestData.code;
            requestData.code = filters.code || "";
            console.log('发送api请求的完整数据:', JSON.stringify(requestData));

            const { url } = this.buildRequestUrl(h5Token, requestData);

            // 清理Cookie字符串，移除换行符和其他无效字符
            const cleanCookie = anchorCookie
                .replace(/[\r\n\t]/g, '')     // 移除换行符和制表符
                .replace(/[\x00-\x1F\x7F]/g, '') // 移除控制字符
                .replace(/[^\x20-\x7E]/g, '')     // 只保留可打印ASCII字符
                .replace(/\s+/g, ' ')         // 将多个空格替换为单个空格
                .trim();                      // 移除首尾空格

            // 调试信息（可选）
            if (anchorCookie.length !== cleanCookie.length) {
                console.log('Cookie已清理，原始长度:', anchorCookie.length, '清理后长度:', cleanCookie.length);
            }

            if (cleanCookie.length === 0) {
                return {
                    success: false,
                    error: 'Cookie清理后为空，可能包含无效字符',
                    data: []
                };
            }

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'accept': '*/*',
                    'accept-language': 'zh-CN,zh;q=0.9',
                    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': '"Windows"',
                    'sec-fetch-dest': 'script',
                    'sec-fetch-mode': 'no-cors',
                    'sec-fetch-site': 'same-site',
                    'cookie': cleanCookie,
                    'referer': 'https://hot.taobao.com/',
                    'referrerPolicy': 'strict-origin-when-cross-origin'
                }
            });

            const responseText = await response.text();

            if (!response.ok) {
                console.error(`HTTP错误 ${response.status}:`, response.statusText);
                console.error('响应内容:', responseText);
                return {
                    success: false,
                    error: `HTTP错误: ${response.status} ${response.statusText}`,
                    data: [],
                    rawResponse: responseText
                };
            }
            let data;
            
            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                if (responseText.trim().startsWith('<')) {
                    return {
                        success: false,
                        error: 'Cookie无效或需要重新登录',
                        data: []
                    };
                }
                return {
                    success: false,
                    error: 'JSON解析失败: ' + parseError.message,
                    data: []
                };
            }

            if (data.data && data.data.data && data.data.data.list) {
                return {
                    success: true,
                    data: data.data.data.list,
                    currentPage: data.data.data.currentPage || pageNum,
                    end: data.data.data.end || false,
                    total: data.data.data.total || 0
                };
            } else {
                let errorMessage = '未知错误';
                if (data.ret && Array.isArray(data.ret)) {
                    errorMessage = data.ret.join(': ');
                } else if (data.msg) {
                    errorMessage = data.msg;
                }
                return {
                    success: false,
                    error: errorMessage,
                    data: []
                };
            }

        } catch (error) {
            console.error('商品采集请求失败:', error);
            return {
                success: false,
                error: error.message,
                data: []
            };
        }
    }

    /**
     * 处理商品数据，提取关键信息
     * @param {array} items 
     * @returns {array}
     */
    processProductData(items) {
        if (!Array.isArray(items)) {
            return [];
        }

        return items.map(item => {
            const extendVal = item.extendVal || {};
            
            // 提取渠道信息
            let channelSlotText = '';
            if (extendVal.channelSlotInfo && extendVal.channelSlotInfo.channelSlotText) {
                channelSlotText = extendVal.channelSlotInfo.channelSlotText;
            }
            
            // 提取365天销量并转换为数字
            let soldQuantity365 = '';
            let soldQuantity365Number = 0;
            
            if (extendVal.soldQuantity365) {
                soldQuantity365 = extendVal.soldQuantity365;
                soldQuantity365Number = this.convertQuantityToNumber(extendVal.soldQuantity365);
            } else if (extendVal.soldQuantityDisplay365) {
                soldQuantity365Number = this.convertQuantityToNumber(extendVal.soldQuantityDisplay365);
                soldQuantity365 = extendVal.soldQuantityDisplay365.value + extendVal.soldQuantityDisplay365.unit;
            } else if (item.soldQuantity365) {
                soldQuantity365 = item.soldQuantity365;
                soldQuantity365Number = this.convertQuantityToNumber(item.soldQuantity365);
            }
            
            // 提取30天主播成交数量并转换为数字
            let placedOrdersIn30 = extendVal.placedOrdersIn30 || '';
            let placedOrdersIn30Number = 0;
            if (placedOrdersIn30) {
                placedOrdersIn30Number = this.convertPlacedOrdersToNumber(placedOrdersIn30);
            }
            
            // 提取销量增长率并转换为数字
            let salesRiseCompareLastWeek = extendVal.sales_rise_compare_last_week || '';
            let salesRiseCompareLastWeekNumber = 0;
            if (salesRiseCompareLastWeek) {
                salesRiseCompareLastWeekNumber = this.convertSalesRiseToNumber(salesRiseCompareLastWeek);
            }
            
            // 提取店铺信息
            let shopName = '';
            let safeShopId = '';
            if (extendVal.shopInfo) {
                shopName = extendVal.shopInfo.shopName || '';
                safeShopId = extendVal.shopInfo.safeShopId || '';
            }

            return {
                itemId: item.itemId || '',
                itemName: item.itemName || '',
                itemPrice: item.itemPrice || '',
                commission: extendVal.commissionPrice || '',
                soldQuantity30: extendVal.soldQuantity30 || item.soldQuantity || '',
                soldQuantityLive7d: extendVal.soldQuantityLive7d || '',
                soldQuantityLive1d: extendVal.soldQuantityLive1d || '',
                soldQuantity365: soldQuantity365,
                soldQuantity365Number: soldQuantity365Number,
                channelSlotText: channelSlotText,
                shopName: shopName,
                safeShopId: safeShopId,
                tcpCommission: extendVal.tcpCommission || '',
                tcpCommissionType: extendVal.tcpCommissionType || '',
                sales_rise_compare_last_week: salesRiseCompareLastWeek,
                sales_rise_compare_last_week_number: salesRiseCompareLastWeekNumber,
                placedOrdersIn30: placedOrdersIn30,
                placedOrdersIn30Number: placedOrdersIn30Number,
                featureTags: item.featureTags || []
            };
        });
    }

    /**
     * 转换销量字符串为数字
     * @param {string|object} quantity 
     * @returns {number}
     */
    convertQuantityToNumber(quantity) {
        if (typeof quantity === 'object' && quantity.value && quantity.unit) {
            const value = parseFloat(quantity.value);
            const unit = quantity.unit;
            
            if (unit === '万') {
                return Math.round(value * 10000);
            } else if (unit === '千') {
                return Math.round(value * 1000);
            } else {
                return Math.round(value);
            }
        }
        
        if (typeof quantity === 'string') {
            const cleanStr = quantity.replace(/[^\d.万千]/g, '');
            const value = parseFloat(cleanStr);
            
            if (isNaN(value)) return 0;
            
            if (quantity.includes('万')) {
                return Math.round(value * 10000);
            } else if (quantity.includes('千')) {
                return Math.round(value * 1000);
            } else {
                return Math.round(value);
            }
        }
        
        return 0;
    }

    /**
     * 转换主播成交数量为数字
     * @param {string} placedOrders 
     * @returns {number}
     */
    convertPlacedOrdersToNumber(placedOrders) {
        return this.convertQuantityToNumber(placedOrders);
    }

    /**
     * 转换销量增长率为数字
     * @param {string} salesRise 
     * @returns {number}
     */
    convertSalesRiseToNumber(salesRise) {
        if (typeof salesRise === 'string') {
            const match = salesRise.match(/([+-]?\d+(?:\.\d+)?)/);
            return match ? parseFloat(match[1]) : 0;
        }
        return 0;
    }

    /**
     * 获取主播信息
     * @param {string} anchorId 
     * @returns {Promise<object|null>}
     */
    async getAnchorInfo(anchorId) {
        try {
            const result = await database.all(
                "SELECT id, anchor_name, anchor_id, anchor_cookie, status FROM anchors WHERE anchor_id = ? AND status = 'active' LIMIT 1",
                [anchorId]
            );

            return result.results && result.results.length > 0 ? result.results[0] : null;
        } catch (error) {
            console.error('获取主播信息失败:', error);
            return null;
        }
    }

    /**
     * 验证Cookie是否有效
     * @param {string} cookie
     * @returns {object}
     */
    validateCookie(cookie) {
        if (!cookie || typeof cookie !== 'string') {
            return { valid: false, error: 'Cookie为空或不是字符串' };
        }

        // 检查是否包含控制字符
        if (/[\x00-\x1F\x7F]/.test(cookie)) {
            return { valid: false, error: 'Cookie包含控制字符' };
        }

        // 检查是否包含换行符
        if (/[\r\n]/.test(cookie)) {
            return { valid: false, error: 'Cookie包含换行符' };
        }

        // 检查是否包含h5Token
        const h5Token = this.extractH5Token(cookie);
        if (!h5Token) {
            return { valid: false, error: 'Cookie中未找到h5Token' };
        }

        return { valid: true, h5Token };
    }

    /**
     * 获取所有活跃主播
     * @returns {Promise<array>}
     */
    async getAllActiveAnchors() {
        try {
            const result = await database.all(
                "SELECT id, anchor_name, anchor_id, anchor_cookie, sort FROM anchors WHERE status = 'active' ORDER BY sort ASC, anchor_name ASC"
            );

            return result.results || [];
        } catch (error) {
            console.error('获取主播列表失败:', error);
            return [];
        }
    }
}

export default ProductCollection;
